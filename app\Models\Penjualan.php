<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Penjualan extends Model
{
    use HasFactory;

    protected $fillable = [
        'kode_pesanan',
        'nama_pelanggan',
        'tanggal_pesanan',
        'status_pesanan',
        'total_harga',
    ];

    protected $casts = [
        'tanggal_pesanan' => 'date',
        'total_harga' => 'decimal:0',
    ];

    public function produks(): BelongsToMany
    {
        return $this->belongsToMany(Produk::class, 'penjualan_produk')
                    ->withPivot('jumlah', 'harga', 'subtotal')
                    ->withTimestamps();
    }
}