<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Stok extends Model
{
    use HasFactory;

    protected $fillable = [
        'bahan_baku_id',
        'periode',
        'jumlah',
        'satuan',
        'harga',
    ];

    protected $casts = [
        'periode' => 'date',
        'jumlah' => 'decimal:1', // Diubah dari integer ke decimal:1
        'harga' => 'decimal:0'
    ];

    public function bahanBaku()
    {
        return $this->belongsTo(BahanBaku::class);
    }

    public static function createFromStokData($periode, $stokData)
    {
        foreach ($stokData as $bahanBakuId => $data) {
            $harga = isset($data['harga']) ? (int) str_replace(['.', ','], '', $data['harga']) : 0;
            
            self::updateOrCreate(
                [
                    'periode' => $periode,
                    'bahan_baku_id' => $bahanBakuId
                ],
                [
                    'jumlah' => $data['jumlah'] ?? 0.0, // Default diubah ke 0.0
                    'satuan' => $data['satuan'] ?? 'kg',
                    'harga' => $harga
                ]
            );
        }
    }

    protected static function boot()
    {
        parent::boot();

        static::saving(function ($model) {
            if (request()->has('stok_data')) {
                $periode = $model->periode;
                self::createFromStokData($periode, request()->stok_data);
                return false;
            }
        });
    }
}