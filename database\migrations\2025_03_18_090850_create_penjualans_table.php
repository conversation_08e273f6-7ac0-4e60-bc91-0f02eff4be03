<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('penjualans', function (Blueprint $table) {
            $table->id();
            $table->string('kode_pesanan')->unique()->index();
            $table->string('nama_pelanggan');
            $table->date('tanggal_pesanan');
            $table->enum('status_pesanan', ['menunggu', 'dikirim', 'selesai', 'dibatalkan'])->default('menunggu');
            $table->decimal('total_harga', 15, 0)->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('penjualans');
    }
};