<?php

namespace App\Filament\Resources\PenjualanResource\Pages;

use App\Filament\Resources\PenjualanResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreatePenjualan extends CreateRecord
{
    protected static string $resource = PenjualanResource::class;

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Tambah Data Penjualan';
    }

    protected function getCreateFormAction(): Actions\Action
    {
        return parent::getCreateFormAction()
            ->label('Simpan');
    }

    protected function getCreateAnotherFormAction(): Actions\Action
    {
        return parent::getCreateAnotherFormAction()
            ->hidden();
    }

    protected function getCancelFormAction(): Actions\Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        // Calculate total_harga from the repeater data before modifying it
        $data['total_harga'] = PenjualanResource::calculateTotal($data['produks'] ?? []);
        return $data;
    }

    protected function afterCreate(): void
    {
        $produksDataFromForm = $this->form->getState()['produks'] ?? [];
        $produksPivotData = [];

        if (is_array($produksDataFromForm)) {
            foreach ($produksDataFromForm as $item) {
                if (isset($item['produk_id']) && !empty($item['produk_id']) && isset($item['harga']) && isset($item['jumlah'])) {
                    $harga = is_numeric($item['harga']) ? (float)$item['harga'] : 0;
                    $jumlah = is_numeric($item['jumlah']) ? (int)$item['jumlah'] : 0;
                    $subtotal = $harga * $jumlah; // Calculate subtotal

                    $produksPivotData[$item['produk_id']] = [
                        'harga' => $harga,
                        'jumlah' => $jumlah,
                        'subtotal' => $subtotal, // Add subtotal to pivot data
                    ];
                }
            }
        }

        if (!empty($produksPivotData)) {
            $this->record->produks()->sync($produksPivotData);
        } else {
             $this->record->produks()->sync([]);
             // Ensure total is zero if no products
             $this->record->update(['total_harga' => 0]);
        }
    }
}