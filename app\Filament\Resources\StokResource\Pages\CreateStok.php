<?php

namespace App\Filament\Resources\StokResource\Pages;

use App\Filament\Resources\StokResource;
use App\Models\Stok;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\UniqueConstraintViolationException;

class CreateStok extends CreateRecord
{
    protected static string $resource = StokResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        try {
            $firstBahanBakuId = array_key_first($data['stok_data']);
            $firstRecord = null;

            foreach ($data['stok_data'] as $bahanBakuId => $values) {
                $stok = Stok::updateOrCreate(
                    [
                        'periode' => $data['periode'],
                        'bahan_baku_id' => $bahanBakuId
                    ],
                    [
                        'jumlah' => $values['jumlah'],
                        'satuan' => $values['satuan'],
                        'harga' => $values['harga'] ?? 0
                    ]
                );

                if ($bahanBakuId === $firstBahanBakuId) {
                    $firstRecord = $stok;
                }
            }

            return $firstRecord;
        } catch (UniqueConstraintViolationException $e) {
            Notification::make()
                ->danger()
                ->title('Periode Sudah Ada')
                ->body('Data stok untuk periode ini sudah ada. Silakan edit data yang ada atau pilih periode lain.')
                ->persistent()
                ->send();

            $this->halt();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Tambah Data Stok';
    }

    protected function getCreateFormAction(): Action
    {
        return parent::getCreateFormAction()
            ->label('Simpan');
    }

    protected function getCreateAnotherFormAction(): Action
    {
        return parent::getCreateAnotherFormAction()
            ->hidden();
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}