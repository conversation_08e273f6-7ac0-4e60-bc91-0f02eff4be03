<?php

namespace Database\Seeders;

use App\Models\BahanBaku;
use App\Models\Produk;
use Illuminate\Database\Seeder;

class ProdukSeeder extends Seeder
{
    public function run(): void
    {
        // Get BahanBaku for roti paris keju
        $bahanBakuParisKeju = BahanBaku::whereIn('nama', [
            'Gula', 'Keju', 'Margarin', 'Me<PERSON>', 'Ragi', 'Telur', 'Tepung Terigu'
        ])->pluck('id');

        // Create or find roti paris keju product
        $rotiParisKeju = Produk::firstOrCreate(
            ['nama_produk' => 'Roti Paris Keju'],
            [
                'harga' => 3000,
                'status' => 'Aktif',
                'bobot_wma_1' => 0.5,
                'bobot_wma_2' => 0.3,
                'bobot_wma_3' => 0.2,
            ]
        );

        // Attach BahanBaku to roti paris keju
        if ($rotiParisKeju->bahanBakus()->count() === 0) {
            $rotiParisKeju->bahanBakus()->attach($bahanBakuParisKeju);
        }

        // Get BahanBaku for roti paris coklat
        $bahanBakuParisCoklat = BahanBaku::whereIn('nama', [
            'Gula', 'Margarin', 'Meses', 'Ragi', 'Telur', 'Tepung Terigu'
        ])->pluck('id');

        // Create or find roti paris coklat product
        $rotiParisCoklat = Produk::firstOrCreate(
            ['nama_produk' => 'Roti Paris Coklat'],
            [
                'harga' => 3000,
                'status' => 'Aktif',
                'bobot_wma_1' => 0.5,
                'bobot_wma_2' => 0.3,
                'bobot_wma_3' => 0.2,
            ]
        );

        // Attach BahanBaku to roti paris coklat
        if ($rotiParisCoklat->bahanBakus()->count() === 0) {
            $rotiParisCoklat->bahanBakus()->attach($bahanBakuParisCoklat);
        }
    }
}