<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Produk extends Model
{
    use HasFactory;

    protected $fillable = [
        'nama_produk',
        'harga',
        'status',
        'bobot_wma_1',
        'bobot_wma_2',
        'bobot_wma_3',
    ];

    protected $casts = [
        'harga' => 'decimal:0',
    ];

    public function bahanBakus(): BelongsToMany
    {
        return $this->belongsToMany(BahanBaku::class, 'bahan_baku_produk')
            ->withTimestamps();
    }

    public function penjualans(): BelongsToMany
    {
        return $this->belongsToMany(Penjualan::class, 'penjualan_produk')
            ->withPivot('jumlah', 'harga')
            ->withTimestamps();
    }
}