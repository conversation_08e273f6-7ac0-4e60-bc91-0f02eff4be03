<?php

namespace App\Filament\Resources\PrediksiResource\Pages;

use App\Filament\Resources\PrediksiResource;
use Filament\Actions\CreateAction;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\ListRecords\Tab;
use Illuminate\Database\Eloquent\Builder;

class ListPrediksis extends ListRecords
{
    protected static string $resource = PrediksiResource::class;

    protected function getHeaderActions(): array
    {
        return [
            CreateAction::make()
                ->label('Buat Prediksi Stok'),
        ];
    }

    public function getTabs(): array
    {
        return [
            'stok' => Tab::make('Stok')
                ->icon('heroicon-o-archive-box')
                ->modifyQueryUsing(fn (Builder $query) => $query),
            'produksi' => Tab::make('Produksi')
                ->icon('heroicon-o-rectangle-stack')
        ];
    }

    public function getDefaultActiveTab(): string | int | null
    {
        return 'stok';
    }
}