<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('stoks', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bahan_baku_id')->constrained()->cascadeOnDelete();
            $table->date('periode');
            $table->decimal('jumlah', 10, 1)->default(0.0);
            $table->string('satuan')->default('kg');
            $table->decimal('harga', 12, 0)->default(0);
            $table->unique(['bahan_baku_id', 'periode']);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('stoks');
    }
};