<?php

namespace Database\Seeders;

use App\Models\Produk;
use App\Models\Produksi;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class ProduksiSeeder extends Seeder
{
    public function run(): void
    {
        // Get products
        $rotiParisKeju = Produk::where('nama_produk', 'Roti Paris Keju')->first();
        $rotiParisCoklat = Produk::where('nama_produk', 'Roti Paris Coklat')->first();

        if (!$rotiParisKeju || !$rotiParisCoklat) {
            $this->command->error('Products not found. Please run ProdukSeeder first.');
            return;
        }

        // Production data for roti paris keju (2024-2025)
        $produksiParisKeju = [
            ['periode' => '2024-03-01', 'jumlah' => 3720],
            ['periode' => '2024-04-01', 'jumlah' => 3680],
            ['periode' => '2024-05-01', 'jumlah' => 3710],
            ['periode' => '2024-06-01', 'jumlah' => 3750],
            ['periode' => '2024-07-01', 'jumlah' => 3690],
            ['periode' => '2024-08-01', 'jumlah' => 3700],
            ['periode' => '2024-09-01', 'jumlah' => 3730],
            ['periode' => '2024-10-01', 'jumlah' => 3670],
            ['periode' => '2024-11-01', 'jumlah' => 3740],
            ['periode' => '2024-12-01', 'jumlah' => 3780],
            ['periode' => '2025-01-01', 'jumlah' => 3650],
            ['periode' => '2025-02-01', 'jumlah' => 3690],
        ];

        // Production data for roti paris coklat (2024-2025)
        $produksiParisCoklat = [
            ['periode' => '2024-03-01', 'jumlah' => 3920],
            ['periode' => '2024-04-01', 'jumlah' => 3880],
            ['periode' => '2024-05-01', 'jumlah' => 3910],
            ['periode' => '2024-06-01', 'jumlah' => 3890],
            ['periode' => '2024-07-01', 'jumlah' => 3870],
            ['periode' => '2024-08-01', 'jumlah' => 3850],
            ['periode' => '2024-09-01', 'jumlah' => 3930],
            ['periode' => '2024-10-01', 'jumlah' => 3830],
            ['periode' => '2024-11-01', 'jumlah' => 3860],
            ['periode' => '2024-12-01', 'jumlah' => 3900],
            ['periode' => '2025-01-01', 'jumlah' => 3850],
            ['periode' => '2025-02-01', 'jumlah' => 3890],
        ];

        // Clear existing production data
        Produksi::truncate();

        // Insert production data for roti paris keju
        foreach ($produksiParisKeju as $data) {
            Produksi::create([
                'produk_id' => $rotiParisKeju->id,
                'periode' => $data['periode'],
                'jumlah' => $data['jumlah'],
                'satuan' => 'pcs',
            ]);
        }

        // Insert production data for roti paris coklat
        foreach ($produksiParisCoklat as $data) {
            Produksi::create([
                'produk_id' => $rotiParisCoklat->id,
                'periode' => $data['periode'],
                'jumlah' => $data['jumlah'],
                'satuan' => 'pcs',
            ]);
        }


    }
}