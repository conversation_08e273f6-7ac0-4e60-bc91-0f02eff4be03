<div class="p-4">
    <h3 class="text-lg font-semibold mb-4">Harga Bahan Baku untuk Periode <?php echo e(\Carbon\Carbon::parse($periode)->translatedFormat('F Y')); ?></h3>
    <!--[if BLOCK]><![endif]--><?php if($stoks->isEmpty()): ?>
        <p>Tidak ada data harga untuk periode ini.</p>
    <?php else: ?>
        <ul class="space-y-2">
            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $stoks; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stok): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="flex justify-between items-center p-2 bg-gray-100 rounded-md">
                    <span class="font-medium"><?php echo e($stok->bahanBaku->nama); ?></span>
                    <span class="text-gray-700">Rp <?php echo e(number_format($stok->harga, 0, ',', '.')); ?> / <?php echo e($stok->satuan); ?></span>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
        </ul>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
</div>
<?php /**PATH C:\laragon\www\jaya_land\resources\views/filament/resources/stok-resource/modals/view-prices.blade.php ENDPATH**/ ?>