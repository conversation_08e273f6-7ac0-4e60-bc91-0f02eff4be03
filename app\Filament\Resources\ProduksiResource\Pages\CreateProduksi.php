<?php

namespace App\Filament\Resources\ProduksiResource\Pages;

use App\Filament\Resources\ProduksiResource;
use App\Models\Produksi;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\UniqueConstraintViolationException;

class CreateProduksi extends CreateRecord
{
    protected static string $resource = ProduksiResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        try {
            $firstProdukId = array_key_first($data['produksi_data']);
            $firstRecord = null;

            foreach ($data['produksi_data'] as $produkId => $values) {
                $produksi = Produksi::updateOrCreate(
                    [
                        'periode' => $data['periode'],
                        'produk_id' => $produkId
                    ],
                    [
                        'jumlah' => (int)($values['jumlah'] ?? 0),
                        'satuan' => $values['satuan'] ?? 'pcs',
                    ]
                );

                if ($produkId === $firstProdukId) {
                    $firstRecord = $produksi;
                }
            }

            return $firstRecord;
        } catch (UniqueConstraintViolationException $e) {
            Notification::make()
                ->danger()
                ->title('Periode Sudah Ada')
                ->body('Data produksi untuk periode ini sudah ada. Silakan edit data yang ada atau pilih periode lain.')
                ->persistent()
                ->send();

            $this->halt();
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string
    {
        return 'Tambah Data Produksi';
    }

    protected function getCreateFormAction(): Action
    {
        return parent::getCreateFormAction()
            ->label('Simpan');
    }

    protected function getCreateAnotherFormAction(): Action
    {
        return parent::getCreateAnotherFormAction()
            ->hidden();
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}