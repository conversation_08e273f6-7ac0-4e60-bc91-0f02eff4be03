<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice Penjualan - {{ $penjualan->kode_pesanan }}</title>
    <style>
        /* --- Unified Base Styles --- */
        body {
            font-family: 'DejaVu Sans', sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            font-size: 12px;
        }
        .container {
            padding: 20px;
        }
        .header, .footer {
            text-align: center;
            margin-bottom: 20px;
        }
        .header h1, .header h2 {
            margin: 0 0 5px 0;
            font-size: 20px;
            color: #000;
        }
        .header p {
            margin: 5px 0;
            font-size: 12px;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
            vertical-align: top;
        }
        th {
            background-color: #f2f2f2;
            font-weight: bold;
            text-align: center;
        }
        /* Utility Classes */
        .text-left { text-align: left; }
        .text-center { text-align: center; }
        .text-right { text-align: right; }
        .bold { font-weight: bold; }
        .no-border { border: none; } /* Utility for layout tables */
        .no-border th, .no-border td { border: none; padding: 5px; }

        /* --- Specific Styles for Penjualan --- */
        .invoice-details, .customer-details {
             margin-bottom: 0;
        }
        .items-table td.number, .items-table th.number {
             text-align: right;
        }
        .total-section {
            margin-top: 20px;
            text-align: right;
        }
        .total-section table {
            width: auto;
            margin-left: auto;
        }
        .total-section th, .total-section td {
             text-align: right;
        }
        .total-section td {
             font-weight: bold;
        }
        hr {
            border: 0;
            border-top: 1px solid #eee;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Invoice Penjualan</h1>
            <p>Jaya Land Bakery</p>
        </div>

        {{-- Customer and Invoice Details Layout Table --}}
        <table class="no-border">
            <tr>
                <td style="width: 50%;">
                    <div class="customer-details">
                        <span class="bold">Kepada:</span><br>
                        {{ $penjualan->nama_pelanggan }}
                    </div>
                </td>
                <td style="width: 50%;">
                    <div class="invoice-details">
                        <span class="bold">Kode Pesanan:</span> {{ $penjualan->kode_pesanan }}<br>
                        <span class="bold">Tanggal Pesanan:</span> {{ $penjualan->tanggal_pesanan->translatedFormat('d F Y') }}<br>
                        <span class="bold">Status:</span> {{ ucfirst($penjualan->status_pesanan) }}
                    </div>
                </td>
            </tr>
        </table>

        <hr>

        <table class="items-table">
            <thead>
                <tr>
                    <th style="width: 5%;">No</th>
                    <th class="text-left">Nama Produk</th>
                    <th class="number" style="width: 15%;">Jumlah</th>
                    <th class="number" style="width: 25%;">Harga</th>
                    <th class="number" style="width: 25%;">Subtotal</th>
                </tr>
            </thead>
            <tbody>
                @foreach ($penjualan->produks as $index => $produk)
                <tr>
                    <td class="text-center">{{ $index + 1 }}</td>
                    <td>{{ $produk->nama_produk }}</td>
                    <td class="number">{{ $produk->pivot->jumlah }}</td>
                    <td class="number">Rp{{ number_format($produk->pivot->harga, 0, ',', '.') }}</td>
                    <td class="number">Rp{{ number_format($produk->pivot->subtotal, 0, ',', '.') }}</td>
                </tr>
                @endforeach
            </tbody>
        </table>

        <div class="total-section">
            <table>
                <tr>
                    <th>Total Harga:</th>
                    <td>Rp{{ number_format($penjualan->total_harga, 0, ',', '.') }}</td>
                </tr>
            </table>
        </div>
    </div>
</body>
</html>