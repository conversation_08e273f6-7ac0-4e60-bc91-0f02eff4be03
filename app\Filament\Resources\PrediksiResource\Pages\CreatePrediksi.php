<?php

namespace App\Filament\Resources\PrediksiResource\Pages;

use App\Filament\Resources\PrediksiResource;
use App\Models\Prediksi;
use Filament\Actions\Action;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;

class CreatePrediksi extends CreateRecord
{
    protected static string $resource = PrediksiResource::class;

    protected function handleRecordCreation(array $data): Model
    {
        $result = Prediksi::calculatePrediction($data['bahan_baku_id']);

        if (!$result['success']) {
            $this->halt();
            $this->notify('danger', $result['message']);
            return null;
        }

        // Check for existing prediction
        $existingPrediction = Prediksi::where('bahan_baku_id', $data['bahan_baku_id'])
            ->where('periode', $result['data']['periode']->format('Y-m-d'))
            ->first();

        if ($existingPrediction) {
            Notification::make()
                ->title('Gagal Membuat Prediksi')
                ->body('Prediksi untuk bahan baku dan periode ini sudah ada.')
                ->danger()
                ->send();
            $this->halt();
            return null;
        }

        return static::getModel()::create([
            'bahan_baku_id' => $data['bahan_baku_id'],
            'periode' => $result['data']['periode'],
            'hasil_prediksi' => $result['data']['prediction'],
            'bobot' => implode(',', $result['data']['weights']),
            'jumlah_periode' => 3,
            'mape' => $result['data']['mape'],
            'detail_prediksi' => $result['data']['predictions'],
            'satuan' => $result['data']['satuan'],
        ]);
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string 
    {
        return 'Prediksi';
    }

    protected function getCreateFormAction(): Action
    {
        return parent::getCreateFormAction()
            ->label('Simpan');
    }

    protected function getCreateAnotherFormAction(): Action
    {
        return parent::getCreateAnotherFormAction()
            ->hidden();
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}