<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('prediksis', function (Blueprint $table) {
            $table->id();
            $table->foreignId('bahan_baku_id')->constrained()->cascadeOnDelete();
            $table->date('periode');
            $table->decimal('hasil_prediksi', 10, 1);
            $table->string('bobot');
            $table->integer('jumlah_periode');
            $table->decimal('mape', 10, 2)->nullable();
            $table->json('detail_prediksi')->nullable();
            $table->string('satuan')->nullable();
            $table->timestamps();
            $table->unique(['bahan_baku_id', 'periode']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('prediksis');
    }
};