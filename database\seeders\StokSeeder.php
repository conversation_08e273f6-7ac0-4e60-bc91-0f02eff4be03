<?php

namespace Database\Seeders;

use App\Models\{BahanBaku, Stok};
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class StokSeeder extends Seeder
{
    public function run(): void
    {
        // Clear existing stock data
        Stok::truncate();

        // Get all raw materials
        $bahanBakus = BahanBaku::all()->keyBy('nama');

        // Stock data based on the provided tables
        $stockData = [
            'Tepung Terigu' => [
                ['periode' => '2024-03-01', 'persediaan' => 205],
                ['periode' => '2024-04-01', 'persediaan' => 185],
                ['periode' => '2024-05-01', 'persediaan' => 190],
                ['periode' => '2024-06-01', 'persediaan' => 175],
                ['periode' => '2024-07-01', 'persediaan' => 200],
                ['periode' => '2024-08-01', 'persediaan' => 192],
                ['periode' => '2024-09-01', 'persediaan' => 188],
                ['periode' => '2024-10-01', 'persediaan' => 210],
                ['periode' => '2024-11-01', 'persediaan' => 180],
                ['periode' => '2024-12-01', 'persediaan' => 200],
                ['periode' => '2025-01-01', 'persediaan' => 205],
                ['periode' => '2025-02-01', 'persediaan' => 200],
            ],
            'Ragi' => [
                ['periode' => '2024-03-01', 'persediaan' => 10],
                ['periode' => '2024-04-01', 'persediaan' => 9],
                ['periode' => '2024-05-01', 'persediaan' => 10.3],
                ['periode' => '2024-06-01', 'persediaan' => 9.5],
                ['periode' => '2024-07-01', 'persediaan' => 10],
                ['periode' => '2024-08-01', 'persediaan' => 10.5],
                ['periode' => '2024-09-01', 'persediaan' => 9.5],
                ['periode' => '2024-10-01', 'persediaan' => 10],
                ['periode' => '2024-11-01', 'persediaan' => 9],
                ['periode' => '2024-12-01', 'persediaan' => 9.5],
                ['periode' => '2025-01-01', 'persediaan' => 10],
                ['periode' => '2025-02-01', 'persediaan' => 10.5],
            ],
            'Telur' => [
                ['periode' => '2024-03-01', 'persediaan' => 67],
                ['periode' => '2024-04-01', 'persediaan' => 75],
                ['periode' => '2024-05-01', 'persediaan' => 70],
                ['periode' => '2024-06-01', 'persediaan' => 69],
                ['periode' => '2024-07-01', 'persediaan' => 71],
                ['periode' => '2024-08-01', 'persediaan' => 67],
                ['periode' => '2024-09-01', 'persediaan' => 65],
                ['periode' => '2024-10-01', 'persediaan' => 63],
                ['periode' => '2024-11-01', 'persediaan' => 60],
                ['periode' => '2024-12-01', 'persediaan' => 62],
                ['periode' => '2025-01-01', 'persediaan' => 64],
                ['periode' => '2025-02-01', 'persediaan' => 60],
            ],
            'Gula' => [
                ['periode' => '2024-03-01', 'persediaan' => 65],
                ['periode' => '2024-04-01', 'persediaan' => 70],
                ['periode' => '2024-05-01', 'persediaan' => 66],
                ['periode' => '2024-06-01', 'persediaan' => 64],
                ['periode' => '2024-07-01', 'persediaan' => 68],
                ['periode' => '2024-08-01', 'persediaan' => 64],
                ['periode' => '2024-09-01', 'persediaan' => 65],
                ['periode' => '2024-10-01', 'persediaan' => 62],
                ['periode' => '2024-11-01', 'persediaan' => 60],
                ['periode' => '2024-12-01', 'persediaan' => 58],
                ['periode' => '2025-01-01', 'persediaan' => 62],
                ['periode' => '2025-02-01', 'persediaan' => 64],
            ],
            'Margarin' => [
                ['periode' => '2024-03-01', 'persediaan' => 50],
                ['periode' => '2024-04-01', 'persediaan' => 55],
                ['periode' => '2024-05-01', 'persediaan' => 48],
                ['periode' => '2024-06-01', 'persediaan' => 52],
                ['periode' => '2024-07-01', 'persediaan' => 49],
                ['periode' => '2024-08-01', 'persediaan' => 53],
                ['periode' => '2024-09-01', 'persediaan' => 50],
                ['periode' => '2024-10-01', 'persediaan' => 48],
                ['periode' => '2024-11-01', 'persediaan' => 46],
                ['periode' => '2024-12-01', 'persediaan' => 47],
                ['periode' => '2025-01-01', 'persediaan' => 45],
                ['periode' => '2025-02-01', 'persediaan' => 48],
            ],
            'Keju' => [
                ['periode' => '2024-03-01', 'persediaan' => 52],
                ['periode' => '2024-04-01', 'persediaan' => 58],
                ['periode' => '2024-05-01', 'persediaan' => 53],
                ['periode' => '2024-06-01', 'persediaan' => 50],
                ['periode' => '2024-07-01', 'persediaan' => 48],
                ['periode' => '2024-08-01', 'persediaan' => 52],
                ['periode' => '2024-09-01', 'persediaan' => 47],
                ['periode' => '2024-10-01', 'persediaan' => 45],
                ['periode' => '2024-11-01', 'persediaan' => 43],
                ['periode' => '2024-12-01', 'persediaan' => 45],
                ['periode' => '2025-01-01', 'persediaan' => 42],
                ['periode' => '2025-02-01', 'persediaan' => 43],
            ],
            'Coklat' => [
                ['periode' => '2024-03-01', 'persediaan' => 38],
                ['periode' => '2024-04-01', 'persediaan' => 40],
                ['periode' => '2024-05-01', 'persediaan' => 35],
                ['periode' => '2024-06-01', 'persediaan' => 36],
                ['periode' => '2024-07-01', 'persediaan' => 34],
                ['periode' => '2024-08-01', 'persediaan' => 32],
                ['periode' => '2024-09-01', 'persediaan' => 35],
                ['periode' => '2024-10-01', 'persediaan' => 31],
                ['periode' => '2024-11-01', 'persediaan' => 30],
                ['periode' => '2024-12-01', 'persediaan' => 33],
                ['periode' => '2025-01-01', 'persediaan' => 31],
                ['periode' => '2025-02-01', 'persediaan' => 32],
            ],
        ];

        // Insert stock data
        foreach ($stockData as $bahanBakuName => $periods) {
            $bahanBaku = $bahanBakus->get($bahanBakuName);
            
            if (!$bahanBaku) {
                $this->command->warn("Bahan baku '{$bahanBakuName}' tidak ditemukan. Melewati...");
                continue;
            }

            foreach ($periods as $periodData) {
                Stok::create([
                    'bahan_baku_id' => $bahanBaku->id,
                    'periode' => Carbon::parse($periodData['periode']),
                    'jumlah' => $periodData['persediaan'],
                    'satuan' => 'kg',
                    'harga' => 0,
                ]);
            }
        }
    }
}