<?php

namespace App\Filament\Widgets;

use App\Models\Stok;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;

class StokTerendahWidget extends BaseWidget
{
    protected static ?int $sort = 3;  // Widget display order (third position)
    protected int | string | array $columnSpan = 'full';  // Widget takes full width
    protected static ?string $heading = 'Bahan Baku Stok Terendah';

    public function table(Table $table): Table
    {
        $latestPeriode = Stok::max('periode');  // Get the most recent stock period

        $query = Stok::query();

        if ($latestPeriode) {
            $query = Stok::where('periode', $latestPeriode)  // Filter by latest period
                         ->with('bahanBaku')
                         ->orderBy('jumlah', 'asc')  // Sort by quantity ascending (lowest first)
                         ->limit(5);  // Get only 5 records
        } else {
            $query = Stok::whereRaw('1 = 0');  // Return empty result if no periods exist
        }

        return $table
            ->query($query)
            ->columns([
                TextColumn::make('bahanBaku.nama')  // Raw material name column
                    ->label('Nama Bahan Baku'),
                TextColumn::make('jumlah')  // Quantity column
                    ->label('Jumlah'),
                TextColumn::make('satuan')  // Unit column
                    ->label('Satuan'),
                TextColumn::make('periode')  // Period column
                    ->label('Periode Stok')
                    ->date('F Y'),  // Format as month year
            ])
            ->paginated(false);
    }
}