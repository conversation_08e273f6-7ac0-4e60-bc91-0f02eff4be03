<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\Artisan;

class SetupDatabase extends Command
{
    protected $signature = 'setup:database';
    protected $description = 'Fresh migrate with seeds and create Filament user';

    public function handle()
    {
        $this->info('Starting database setup...');

        // Run fresh migration with seeds
        $this->info('Running fresh migration with seeds...');
        Artisan::call('migrate:fresh --seed');
        $this->info('Migration completed successfully.');

        // Create Filament user
        $this->info('Creating Filament user...');
        Artisan::call('make:filament-user', [
            '--name' => 'Beni',
            '--email' => '<EMAIL>',
            '--password' => 'beni',
        ]);
        $this->info('Filament user created successfully.');

        $this->info('Database setup completed!');
        return Command::SUCCESS;
    }
}