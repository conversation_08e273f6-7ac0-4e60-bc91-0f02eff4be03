<?php

namespace App\Filament\Widgets;

use App\Models\{BahanBaku, Penjualan, Produk};
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Illuminate\Support\Number;

class StatsOverviewWidget extends BaseWidget
{
    protected function getStats(): array
    {
        $totalProdukAktif = Produk::where('status', 'Aktif')->count();  // Count active products only
        $totalBahanBaku = BahanBaku::count();  // Count all raw materials
        $pesananMenunggu = Penjualan::where('status_pesanan', 'menunggu')->count();  // Count pending orders
        $totalPenjualanBulanIni = Penjualan::where('status_pesanan', 'selesai')  // Only count completed sales
                                        ->whereMonth('tanggal_pesanan', now()->month)  // Filter by current month
                                        ->whereYear('tanggal_pesanan', now()->year)  // Filter by current year
                                        ->sum('total_harga');  // Sum the total prices

        return [
            Stat::make('Produk Aktif', $totalProdukAktif)  // Display active products count
                ->description('Jumlah produk siap dijual')
                ->descriptionIcon('heroicon-m-cake')
                ->color('success'),
            Stat::make('Bahan Baku', $totalBahanBaku)  // Display raw materials count
                ->description('Jumlah bahan baku terdaftar')
                ->descriptionIcon('heroicon-m-cube')
                ->color('info'),
            Stat::make('Pesanan Menunggu', $pesananMenunggu)  // Display pending orders count
                ->description('Jumlah pesanan belum diproses')
                ->descriptionIcon('heroicon-m-clock')
                ->color('warning'),
            Stat::make('Penjualan Bulan Ini', 'Rp' . Number::format($totalPenjualanBulanIni, precision: 0, locale: 'id'))  // Format currency with Indonesian locale
                ->description('Total penjualan selesai bulan ini')
                ->descriptionIcon('heroicon-m-currency-dollar')
                ->color('primary'),
        ];
    }
}