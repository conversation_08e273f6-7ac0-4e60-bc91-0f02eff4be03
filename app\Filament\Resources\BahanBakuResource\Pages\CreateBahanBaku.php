<?php

namespace App\Filament\Resources\BahanBakuResource\Pages;

use App\Filament\Resources\BahanBakuResource;
use App\Models\Stok;
use Filament\Actions\Action;
use Filament\Resources\Pages\CreateRecord;

class CreateBahanBaku extends CreateRecord
{
    protected static string $resource = BahanBakuResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $data['kode'] = strtoupper($data['kode']);
        $data['nama'] = ucwords(strtolower($data['nama']));
        return $data;
    }

    protected function afterCreate(): void
    {
        // Get all unique periods from existing stock records
        $existingPeriods = Stok::select('periode')
            ->distinct()
            ->orderBy('periode')
            ->pluck('periode');

        // Create stock records with 0 value for all existing periods
        foreach ($existingPeriods as $periode) {
            Stok::create([
                'bahan_baku_id' => $this->record->id,
                'periode' => $periode,
                'jumlah' => 0
            ]);
        }
    }

    protected function getRedirectUrl(): string
    {
        return $this->getResource()::getUrl('index');
    }

    public function getTitle(): string 
    {
        return 'Tambah Data Bahan Baku';
    }

    protected function getCreateFormAction(): Action
    {
        return parent::getCreateFormAction()
            ->label('Simpan');
    }

    protected function getCreateAnotherFormAction(): Action
    {
        return parent::getCreateAnotherFormAction()
            ->hidden();
    }

    protected function getCancelFormAction(): Action
    {
        return parent::getCancelFormAction()
            ->label('Batal');
    }
}