<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Prediksi extends Model
{
    use HasFactory;

    protected $fillable = [
        'bahan_baku_id',
        'periode',
        'hasil_prediksi',
        'bobot',
        'jumlah_periode',
        'mape',
        'detail_prediksi',
        'satuan'
    ];

    protected $casts = [
        'periode' => 'date',
        'hasil_prediksi' => 'decimal:1',
        'jumlah_periode' => 'integer',
        'mape' => 'decimal:2',
        'detail_prediksi' => 'array'
    ];

    public function bahanBaku()
    {
        return $this->belongsTo(BahanBaku::class);
    }

    public static function calculatePrediction($bahanBakuId)
    {
        // Get last 12 months of data, ordered from newest to oldest
        $historicalData = Stok::where('bahan_baku_id', $bahanBakuId)
            ->orderBy('periode', 'desc')
            ->take(12)
            ->get();
    
        if ($historicalData->count() < 3) {
            return [
                'success' => false,
                'message' => 'Data historis minimal 3 bulan terakhir diperlukan untuk melakukan prediksi.'
            ];
        }
    
        // Reverse the collection for processing while maintaining original order in database
        $processedData = $historicalData->reverse()->values();

        // Get WMA weights from BahanBaku model
        $bahanBaku = BahanBaku::find($bahanBakuId);
        $weights = [$bahanBaku->bobot_wma_1, $bahanBaku->bobot_wma_2, $bahanBaku->bobot_wma_3];
        $totalWeight = array_sum($weights);
        $totalMAPE = 0;
        $countMAPE = 0;
        $allPeriods = [];
    
        // Process all periods using reversed data
        for ($i = 0; $i < $processedData->count(); $i++) {
            $period = [
                'periode' => $processedData[$i]->periode,
                'aktual' => $processedData[$i]->jumlah,
                'satuan' => $processedData[$i]->satuan,
                'prediksi' => null,
                'error' => null,
                'mape' => null,
                'calculation_data' => null
            ];
    
            // Start predictions after first 3 months
            if ($i >= 3) {
                $previousThreeMonths = [
                    $processedData[$i-1],
                    $processedData[$i-2],
                    $processedData[$i-3]
                ];
                
                $prediction = 0;
                $calculationSteps = [];
                
                // Calculate prediction using previous 3 months
                for ($j = 0; $j < 3; $j++) {
                    $weightedValue = $previousThreeMonths[$j]->jumlah * ($weights[$j] / $totalWeight);
                    $prediction += $weightedValue;
                    $calculationSteps[] = [
                        'month' => $previousThreeMonths[$j]->periode->translatedFormat('F Y'),
                        'value' => $previousThreeMonths[$j]->jumlah,
                        'weight' => $weights[$j],
                        'weighted_value' => $weightedValue
                    ];
                }
                
                $prediction = round($prediction, 1);
                $actualValue = $processedData[$i]->jumlah;
                $error = $actualValue - $prediction;
                $mape = $actualValue != 0 ? (abs($error) / $actualValue) * 100 : 0;
                
                if ($actualValue != 0) {
                    $totalMAPE += $mape;
                    $countMAPE++;
                }
    
                $period['prediksi'] = $prediction;
                $period['error'] = $error;
                $period['mape'] = $mape;
                $period['calculation_data'] = $calculationSteps;
            }
    
            $allPeriods[] = $period;
        }
    
        // Reverse back the periods to match the original order (newest first)
        $allPeriods = array_reverse($allPeriods);
    
        // Calculate next month prediction using last 3 months (from original historicalData)
        $lastThreeMonths = $historicalData->take(3)->values();
        $finalPrediction = 0;
        $finalCalculationSteps = [];
        
        // Get the latest unit used
        $latestUnit = $historicalData->first()->satuan;
        
        foreach ($lastThreeMonths as $index => $data) {
            $weightedValue = $data->jumlah * ($weights[$index] / $totalWeight);
            $finalPrediction += $weightedValue;
            $finalCalculationSteps[] = [
                'month' => $data->periode->translatedFormat('F Y'),
                'value' => $data->jumlah,
                'weight' => $weights[$index],
                'weighted_value' => $weightedValue
            ];
        }
    
        return [
            'success' => true,
            'data' => [
                'prediction' => round($finalPrediction, 1),
                'periode' => $historicalData->first()->periode->addMonth(), // Changed from last() to first()
                'mape' => $countMAPE > 0 ? $totalMAPE / $countMAPE : 0,
                'historicalData' => $historicalData,
                'predictions' => $allPeriods,
                'weights' => $weights,
                'final_calculation' => $finalCalculationSteps,
                'satuan' => $latestUnit
            ]
        ];
    }
}