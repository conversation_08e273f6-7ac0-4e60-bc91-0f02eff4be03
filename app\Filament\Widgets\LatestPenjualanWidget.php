<?php

namespace App\Filament\Widgets;

use App\Filament\Resources\PenjualanResource;
use App\Models\Penjualan;
use Filament\Tables\{Actions\Action, Columns\TextColumn, Table};
use Filament\Widgets\TableWidget as BaseWidget;
use Illuminate\Support\Number;

class LatestPenjualanWidget extends BaseWidget
{
    protected static ?int $sort = 2;  // Widget display order (second position)
    protected int | string | array $columnSpan = 'full';  // Widget takes full width
    protected static ?string $heading = 'Penjualan Terbaru';

    public function table(Table $table): Table
    {
        return $table
            ->query(PenjualanResource::getEloquentQuery()->latest('tanggal_pesanan')->limit(5))  // Get 5 most recent orders
            ->columns([
                TextColumn::make('kode_pesanan')
                    ->label('Kode Pesanan'),
                TextColumn::make('nama_pelanggan')
                    ->label('<PERSON>a <PERSON>nggan'),
                TextColumn::make('tanggal_pesanan')
                    ->label('Tanggal')
                    ->date('d F Y'),  // Format date as day month year
                TextColumn::make('total_harga')
                    ->label('Total Harga')
                    ->formatStateUsing(fn (string $state): string => 'Rp' . Number::format((float) $state, precision: 0, locale: 'id')),  // Format as Indonesian currency
                TextColumn::make('status_pesanan')
                    ->label('Status')
                    ->badge()
                    ->color(fn (string $state): string => match ($state) {  // Color based on status
                        'menunggu' => 'warning',
                        'dikirim' => 'info',
                        'selesai' => 'success',
                        'dibatalkan' => 'danger',
                        default => 'gray',
                    })
                    ->formatStateUsing(fn (string $state): string => ucfirst($state)),  // Capitalize first letter
            ])
             ->actions([
                 Action::make('Lihat')  // View action button
                     ->url(fn (Penjualan $record): string => PenjualanResource::getUrl('edit', ['record' => $record])),  // Link to edit page
             ]);
    }
}